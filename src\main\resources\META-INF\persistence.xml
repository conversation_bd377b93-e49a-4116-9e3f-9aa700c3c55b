<?xml version="1.0" encoding="UTF-8" ?>
<persistence version="3.0"
             xmlns="https://jakarta.ee/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence https://jakarta.ee/xml/ns/persistence/persistence_3_0.xsd">
    <persistence-unit name="QuanLyCuaHangPU" transaction-type="RESOURCE_LOCAL">
        <!-- Original entities -->
        <class>model.BienTheSanPham</class>
        <class>model.ChiTietHoaDon</class>
        <class>model.DanhMuc</class>
        <class>model.HoaDon</class>
        <class>model.KhachHang</class>
        <class>model.NhanVien</class>
        <class>model.SanPham</class>
        <class>model.MauSac</class>
        <class>model.KichThuoc</class>
        <class>model.TaiKhoan</class>
        
        <!-- New feature entities -->
        <class>model.BaoCao</class>
        <class>model.ThongKeDoanhThu</class>
        <class>model.ThongKeSanPham</class>
        <class>model.NhaCungCap</class>
        <class>model.DonDatHang</class>
        <class>model.ChiTietDatHang</class>
        <class>model.ChuongTrinhKhuyenMai</class>
        <class>model.HinhThucThanhToan</class>
        <class>model.PhieuDoiTra</class>
        <class>model.ChiTietPhieuDoiTra</class>
        <class>model.TheThanThiet</class>
        <class>model.LichSuDiem</class>
        <!-- Config JDBC -->
        <properties>
            <!-- JPA -->
            <property name="jakarta.persistence.jdbc.url" value="********************************************************************************************"/>
            <property name="jakarta.persistence.jdbc.user" value="sa"/>
            <property name="jakarta.persistence.jdbc.password" value="123"/>
            <property name="jakarta.persistence.jdbc.driver" value="com.microsoft.sqlserver.jdbc.SQLServerDriver"/>

            <!-- Hibernate -->
            <property name="hibernate.connection.provider_class" value="org.hibernate.hikaricp.internal.HikariCPConnectionProvider"/>
            <property name="hibernate.hikari.minimumIdle" value="3"/>
            <property name="hibernate.hikari.maximumPoolSize" value="10"/>
            <property name="hibernate.hikari.idleTimeout" value="30000"/>
            <property name="hibernate.hikari.connectionTimeout" value="30000"/>
            <property name="hibernate.hikari.poolName" value="MyPool"/>
            <property name="hibernate.hikari.autoCommit" value="false"/>

            <!-- Connection and transaction settings -->
            <property name="hibernate.connection.autocommit" value="false"/>
            <property name="hibernate.jdbc.batch_size" value="20"/>
            <property name="hibernate.order_inserts" value="true"/>
            <property name="hibernate.order_updates" value="true"/>

            <!-- Debugging -->
            <property name="hibernate.show_sql" value="true"/>
            <property name="hibernate.format_sql" value="true"/>
            <property name="hibernate.hbm2ddl.auto" value="update"/>
        </properties>
    </persistence-unit>
</persistence>
