<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5e15e1e5-b028-43c8-96df-aca011b72e0a" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/script.sql" beforeDir="false" afterPath="$PROJECT_DIR$/script.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/controller/BienTheSanPhamController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/controller/BienTheSanPhamController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/controller/HoaDonController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/controller/HoaDonController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/dao/impl/BienTheSanPhamDAO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/dao/impl/BienTheSanPhamDAO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/dao/impl/ChiTietHoaDonDAO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/dao/impl/ChiTietHoaDonDAO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/dao/impl/HoaDonDAO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/dao/impl/HoaDonDAO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/dao/impl/KhachHangDAO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/dao/impl/KhachHangDAO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/dao/interfaces/IBienTheSanPhamDAO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/dao/interfaces/IBienTheSanPhamDAO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/dao/interfaces/IHoaDonDAO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/dao/interfaces/IHoaDonDAO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/service/impl/PhieuDoiTraServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/service/impl/PhieuDoiTraServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/view/BienTheSanPhamUI.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/view/BienTheSanPhamUI.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/view/ChiTietHoaDonDialog.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/view/ChiTietHoaDonDialog.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/view/HoaDonUI.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/view/HoaDonUI.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/view/PhieuDoiTraDetailUI.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/view/PhieuDoiTraDetailUI.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/view/PhieuDoiTraFormUI.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/view/PhieuDoiTraFormUI.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/view/ThemPhieuDoiTraDialog.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/view/ThemPhieuDoiTraDialog.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/view/ThemSanPhamDialog.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/view/ThemSanPhamDialog.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="31SgqXB0BYa7TQmzTxjdTXn4AKF" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.DoAnJava.executor&quot;: &quot;Run&quot;,
    &quot;Maven.QLCHQuanAo [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.QLCHQuanAo [install].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;G:/My Drive/Study/University/JAVA/DoAn&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;to.speed.mode.migration.done&quot;: &quot;true&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="DoAnJava" type="Application" factoryName="Application">
      <option name="MAIN_CLASS_NAME" value="main.Main" />
      <module name="QLCHQuanAo" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-bf35d07a577b-intellij.indexing.shared.core-IU-252.23892.409" />
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-IU-252.23892.409" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5e15e1e5-b028-43c8-96df-aca011b72e0a" name="Changes" comment="" />
      <created>1755523078624</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755523078624</updated>
      <workItem from="1755523080003" duration="9776000" />
      <workItem from="1755929251129" duration="11708000" />
      <workItem from="1756015687833" duration="1176000" />
      <workItem from="1756034214168" duration="622000" />
      <workItem from="1756126632215" duration="6359000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>